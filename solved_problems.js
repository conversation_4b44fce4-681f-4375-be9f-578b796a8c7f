
// 1. Reverse a String
let reverse = (str) => {
    let s = '';
    for (let i = str.length - 1; i >= 0; i--) {
        s += str[i];
    }
    return s;
}
console.log("Reversed:", reverse("hello")); // Output: "olleh"

// 2. Two Sum Problem
let twoSum = (nums, target) => {
    let arr = {};
    for (let i = 0; i < nums.length; i++) {
        if (arr[target - nums[i]] !== undefined) {
            return [arr[target - nums[i]], i];
        } else {
            arr[nums[i]] = i;
        }
    }
}
console.log("Two Sum:", twoSum([2, 7, 11, 15], 9)); // Output: [0, 1]

// 3. Move All Zeros to End
let moveZero = (arr) => {
    let newArray = [];
    for (let i = 0; i < arr.length; i++) {
        if (arr[i] !== 0) {
            newArray.push(arr[i]);
        }
    }
    let a = arr.length - newArray.length;
    for (let j = 0; j < a; j++) {
        newArray.push(0);
    }
    return newArray;
}
console.log("Move Zeroes:", moveZero([0, 1, 0, 3, 12])); // Output: [1, 3, 12, 0, 0]

// 4. Check Anagram
let Anagram = (s, t) => {
    if (s.length !== t.length) return false;
    let char = {};
    for (let i of s) {
        char[i] = (char[i] || 0) + 1;
    }
    for (let j of t) {
        if (!char[j]) return false;
        char[j]--;
    }
    return true;
}
console.log("Anagram:", Anagram("anagram", "nagaram")); // Output: true

// 5. Sort Using Built-in Sort
let sortWithBuiltin = (arr) => {
    return arr.sort((a, b) => a - b);
}
console.log("Sort (Built-in):", sortWithBuiltin([2, 5, 1, 12, 8, 35, 6, 87]));

// 6. Sort Without Built-in Sort (Bubble Sort)
let sortWithoutBuiltin = (arr) => {
    for (let i = 0; i < arr.length - 1; i++) {
        for (let j = 0; j < arr.length - 1 - i; j++) {
            if (arr[j] > arr[j + 1]) {
                let temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }
    return arr;
}
console.log("Sort (Bubble Sort):", sortWithoutBuiltin([2, 5, 1, 12, 8, 35, 6, 87]));
